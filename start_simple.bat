@echo off
chcp 65001 >nul
echo 启动LLM评估平台开发环境...
echo.

:: 启动后端
echo 启动后端服务器...
start "后端-Flask" cmd /k "python run_api.py"

:: 等待2秒
timeout /t 2 /nobreak >nul

:: 启动前端
echo 启动前端开发服务器...
start "前端-Vue" cmd /k "cd llm-eval-frontend && npm run dev"

:: 等待3秒
timeout /t 3 /nobreak >nul

echo.
echo ✅ 启动完成！
echo 前端: http://localhost:3000
echo 后端: http://localhost:5000
echo.

:: 打开浏览器
start http://localhost:3000

pause
