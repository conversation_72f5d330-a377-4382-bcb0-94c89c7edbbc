<template>
  <div>
    <n-space justify="space-between" align="center" style="margin-bottom: 24px;">
      <div>
        <h1 style="margin: 0; font-size: 24px; font-weight: 600;">测试集管理</h1>
        <p style="margin: 8px 0 0 0; color: #909399;">
          管理评估数据集，支持多种格式的数据导入和预处理
        </p>
      </div>
      <n-button type="primary">
        <template #icon>
          <n-icon><Add /></n-icon>
        </template>
        添加测试集
      </n-button>
    </n-space>

    <n-card>
      <n-empty description="测试集管理功能正在开发中" size="large">
        <template #icon>
          <n-icon size="64" color="#d9d9d9">
            <Library />
          </n-icon>
        </template>
        <template #extra>
          <n-space>
            <n-button disabled>
              <template #icon>
                <n-icon><Time /></n-icon>
              </template>
              功能开发中
            </n-button>
            <n-button type="primary" @click="$router.push('/')">
              返回首页
            </n-button>
          </n-space>
        </template>
      </n-empty>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import {
  Add,
  Library,
  Time
} from '@vicons/ionicons5'
</script>
