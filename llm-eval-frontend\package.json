{"name": "llm-eval-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "commit": "cz", "prepare": "husky"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vicons/antd": "^0.13.0", "@vicons/fluent": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "element-plus": "^2.10.5", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "uuid": "^11.1.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/node": "^24.1.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "axios": "^1.11.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}