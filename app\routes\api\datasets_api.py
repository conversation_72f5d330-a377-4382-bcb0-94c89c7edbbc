# 数据集管理相关API
from flask import Blueprint, request, current_app
from app.models import Dataset
from app.routes.api.common import (
    api_response, api_error, api_auth_required, 
    get_current_api_user, validate_json_data, paginate_query
)

bp = Blueprint('datasets_api', __name__, url_prefix='/datasets')

@bp.route('', methods=['GET'])
@api_auth_required
def api_get_datasets():
    """获取数据集列表"""
    try:
        user = get_current_api_user()
        if not user:
            return api_error('用户未找到', 404)
        
        # 获取查询参数
        search = request.args.get('search', '').strip()
        dataset_type = request.args.get('type', '').strip()
        
        # 构建查询 - 只显示用户自己的数据集
        query = Dataset.query.filter_by(user_id=user.id)
        
        # 搜索过滤
        if search:
            query = query.filter(
                Dataset.name.contains(search) |
                Dataset.description.contains(search)
            )
        
        # 类型过滤
        if dataset_type:
            query = query.filter_by(dataset_type=dataset_type)
        
        # 排序
        query = query.order_by(Dataset.created_at.desc())
        
        # 分页
        pagination_data = paginate_query(query)
        
        # 序列化数据集数据
        datasets_data = []
        for dataset in pagination_data['items']:
            dataset_dict = {
                'id': dataset.id,
                'name': dataset.name,
                'description': dataset.description,
                'dataset_type': dataset.dataset_type,
                'file_path': dataset.file_path,
                'file_size': dataset.file_size,
                'record_count': dataset.record_count,
                'created_at': dataset.created_at.isoformat() if dataset.created_at else None,
                'updated_at': dataset.updated_at.isoformat() if dataset.updated_at else None
            }
            datasets_data.append(dataset_dict)
        
        return api_response(
            success=True,
            data={
                'datasets': datasets_data,
                'pagination': {
                    'total': pagination_data['total'],
                    'page': pagination_data['page'],
                    'per_page': pagination_data['per_page'],
                    'pages': pagination_data['pages'],
                    'has_next': pagination_data['has_next'],
                    'has_prev': pagination_data['has_prev']
                }
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"获取数据集列表API错误: {e}")
        return api_error('获取数据集列表失败', 500)

@bp.route('', methods=['POST'])
@api_auth_required
@validate_json_data(['name', 'dataset_type'])
def api_create_dataset():
    """创建新数据集"""
    try:
        user = get_current_api_user()
        if not user:
            return api_error('用户未找到', 404)
        
        data = request.get_json()
        
        # 验证数据集名称唯一性
        existing_dataset = Dataset.query.filter_by(
            name=data['name'],
            user_id=user.id
        ).first()
        
        if existing_dataset:
            return api_error('数据集名称已存在', 400)
        
        # 创建数据集
        dataset = Dataset(
            name=data['name'].strip(),
            description=data.get('description', '').strip() or None,
            dataset_type=data['dataset_type'],
            user_id=user.id
        )
        
        from app import db
        db.session.add(dataset)
        db.session.commit()
        
        current_app.logger.info(f"用户 {user.username} 创建数据集: {dataset.name}")
        
        return api_response(
            success=True,
            data={
                'id': dataset.id,
                'name': dataset.name,
                'description': dataset.description,
                'dataset_type': dataset.dataset_type,
                'created_at': dataset.created_at.isoformat() if dataset.created_at else None
            },
            message='数据集创建成功',
            status_code=201
        )
        
    except Exception as e:
        current_app.logger.error(f"创建数据集API错误: {e}")
        return api_error('创建数据集失败', 500)

@bp.route('/<int:dataset_id>', methods=['GET'])
@api_auth_required
def api_get_dataset(dataset_id):
    """获取单个数据集详情"""
    try:
        user = get_current_api_user()
        if not user:
            return api_error('用户未找到', 404)
        
        # 查找用户自己的数据集
        dataset = Dataset.query.filter_by(
            id=dataset_id,
            user_id=user.id
        ).first()
        
        if not dataset:
            return api_error('数据集未找到', 404)
        
        dataset_data = {
            'id': dataset.id,
            'name': dataset.name,
            'description': dataset.description,
            'dataset_type': dataset.dataset_type,
            'file_path': dataset.file_path,
            'file_size': dataset.file_size,
            'record_count': dataset.record_count,
            'created_at': dataset.created_at.isoformat() if dataset.created_at else None,
            'updated_at': dataset.updated_at.isoformat() if dataset.updated_at else None
        }
        
        return api_response(success=True, data=dataset_data)
        
    except Exception as e:
        current_app.logger.error(f"获取数据集详情API错误: {e}")
        return api_error('获取数据集详情失败', 500)

@bp.route('/<int:dataset_id>', methods=['DELETE'])
@api_auth_required
def api_delete_dataset(dataset_id):
    """删除数据集"""
    try:
        user = get_current_api_user()
        if not user:
            return api_error('用户未找到', 404)
        
        # 查找用户自己的数据集
        dataset = Dataset.query.filter_by(
            id=dataset_id,
            user_id=user.id
        ).first()
        
        if not dataset:
            return api_error('数据集未找到', 404)
        
        dataset_name = dataset.name
        
        # 删除数据集文件（如果存在）
        if dataset.file_path:
            import os
            try:
                if os.path.exists(dataset.file_path):
                    os.remove(dataset.file_path)
            except Exception as e:
                current_app.logger.warning(f"删除数据集文件失败: {e}")
        
        # 删除数据库记录
        from app import db
        db.session.delete(dataset)
        db.session.commit()
        
        current_app.logger.info(f"用户 {user.username} 删除数据集: {dataset_name}")
        
        return api_response(
            success=True,
            message=f'数据集 "{dataset_name}" 删除成功'
        )
        
    except Exception as e:
        current_app.logger.error(f"删除数据集API错误: {e}")
        return api_error('删除数据集失败', 500)

@bp.route('/<int:dataset_id>/data', methods=['GET'])
@api_auth_required
def api_get_dataset_data(dataset_id):
    """获取数据集数据（预览）"""
    try:
        user = get_current_api_user()
        if not user:
            return api_error('用户未找到', 404)
        
        # 查找用户自己的数据集
        dataset = Dataset.query.filter_by(
            id=dataset_id,
            user_id=user.id
        ).first()
        
        if not dataset:
            return api_error('数据集未找到', 404)
        
        if not dataset.file_path:
            return api_error('数据集文件不存在', 404)
        
        # 读取数据集文件（限制预览行数）
        limit = int(request.args.get('limit', 100))
        limit = min(limit, 1000)  # 最大1000行
        
        try:
            import json
            import os
            
            if not os.path.exists(dataset.file_path):
                return api_error('数据集文件不存在', 404)
            
            data_preview = []
            with open(dataset.file_path, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if i >= limit:
                        break
                    try:
                        data_preview.append(json.loads(line.strip()))
                    except json.JSONDecodeError:
                        continue
            
            return api_response(
                success=True,
                data={
                    'preview': data_preview,
                    'total_shown': len(data_preview),
                    'total_records': dataset.record_count,
                    'limit': limit
                }
            )
            
        except Exception as e:
            current_app.logger.error(f"读取数据集文件错误: {e}")
            return api_error('读取数据集文件失败', 500)
        
    except Exception as e:
        current_app.logger.error(f"获取数据集数据API错误: {e}")
        return api_error('获取数据集数据失败', 500)
