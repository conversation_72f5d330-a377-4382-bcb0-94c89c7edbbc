# LLM-Eval 项目说明文档

## 项目概述

LLM-Eval 是一个大型语言模型（LLM）评估平台，旨在提供全面的模型评估工具集，包括模型效果评估、RAG（检索增强生成）评估以及模型性能评估功能。该平台支持多种数据集，并允许用户自定义模型配置，为研究人员和开发者提供了一个便捷的工具来评估和比较不同LLM的表现。

## 核心功能

### 1. 模型管理
- 支持多种模型类型的注册和管理
- 提供系统内置模型和用户自定义模型
- 模型参数配置（temperature、max_tokens等）
- API密钥安全存储

### 2. 数据集管理
- 支持多种格式的数据集导入和管理
- 数据集分类系统（函数调用、创作、多模态等12个主要类别）
- 内置常用评估数据集（如C-Eval）
- 支持自定义数据集上传和配置

### 3. 模型效果评估
- 基于多种数据集的模型效果评估
- 支持使用裁判模型进行自动化评估
- 可配置评估参数（并发数、批处理大小等）
- 提供详细的评估结果和反馈

### 4. RAG评估
- 专门针对检索增强生成系统的评估
- 多维度评估指标（相关性、忠实性、答案正确性等）
- 支持自定义嵌入模型和裁判模型
- 提供详细的上下文评估结果

### 5. 模型性能评估
- 评估模型在不同负载下的性能表现
- 支持并发请求测试
- 提供响应时间、吞吐量等关键指标
- 性能结果可视化展示

### 6. 聊天对话功能
- 提供与模型的实时交互界面
- 支持多轮对话和多模型并行对比
- 对话历史记录和管理
- 基于WebSocket/SSE的流式响应
- 使用成熟的对话UI组件（支持Markdown渲染、代码高亮）

## 技术架构

### 前端（正在迁移中）
- **当前**：基于 Flask 的 Web 框架，使用 Jinja2 模板引擎 + Bootstrap
- **目标**：Vue 3.4 + TypeScript 5.0 + Vite 5.0 单页应用(SPA)
- **UI组件库**：Element Plus（企业级Vue3组件库）
- **状态管理**：Pinia（Vue3官方推荐）
- **实时通信**：WebSocket/SSE流式响应

### 后端
- **Web框架**: Flask
- **数据库**: MySQL (使用 SQLAlchemy ORM)
- **用户认证**: 简化的内部访问控制（无需严格认证）
- **表单处理**: Flask-WTF (包含 CSRF 保护)
- **数据库迁移**: Flask-Migrate
- **日志系统**: Python logging 模块

### 数据模型
系统包含以下主要数据模型：
- **User**: 用户管理（简化的内部用户标识）
- **AIModel**: 模型注册和配置
- **ChatSession/ChatMessage**: 聊天对话记录
- **Dataset**: 数据集管理
- **ModelEvaluation**: 模型效果评估任务
- **RAGEvaluation**: RAG评估任务
- **PerformanceEvalTask**: 模型性能评估任务

## 项目结构

```
llm-eval/
├── app/                    # 应用主目录
│   ├── adapter/            # 数据集适配器
│   ├── config.py           # 配置文件
│   ├── forms.py            # 表单定义
│   ├── models.py           # 数据模型
│   ├── routes/             # 路由定义
│   │   ├── auth_routes.py      # 认证相关路由（简化版）
│   │   ├── chat_routes.py      # 聊天功能路由
│   │   ├── dataset_routes.py   # 数据集管理路由
│   │   ├── evaluation_routes.py # 模型评估路由
│   │   ├── models_routes.py    # 模型管理路由
│   │   ├── perf_eval_routes.py # 性能评估路由
│   │   └── rag_eval_routes.py  # RAG评估路由
│   ├── services/           # 业务逻辑
│   ├── static/             # 静态资源（旧版前端）
│   ├── templates/          # HTML模板（旧版前端）
│   └── utils/              # 工具函数
├── web/                    # 新版Vue3前端项目
│   ├── src/
│   │   ├── api/           # API接口
│   │   ├── components/    # Vue组件
│   │   ├── pages/         # 页面组件
│   │   ├── stores/        # Pinia状态管理
│   │   └── types/         # TypeScript类型定义
├── docker/                 # Docker相关配置
├── migrations/            # 数据库迁移文件
├── run.py                  # 应用入口
└── requirements.txt        # 依赖列表
```

## 部署方式

### 本地部署（开发环境）
1. 后端启动：
   ```bash
   pip install -r requirements.txt
   python run.py
   ```

2. 前端开发（新版Vue3）：
   ```bash
   cd web
   npm install
   npm run dev
   ```

### Docker部署
1. 使用提供的 Dockerfile 和 docker-compose.yml
2. 执行: `docker-compose up`

### 生产环境部署
1. 前端构建：
   ```bash
   cd web
   npm run build
   ```
2. 使用Nginx部署构建后的静态文件
3. 配置反向代理到Flask后端API

## 环境配置

项目支持通过环境变量进行配置，主要配置项包括：
- 数据库连接（DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME）
- 系统模型配置（SYSTEM_PROVIDER_API_KEY, SYSTEM_PROVIDER_BASE_URL）
- 文件上传路径（DATA_UPLOADS_DIR, DATA_OUTPUTS_DIR）
- 文件大小限制（DATASET_MAX_FILE_SIZE）
- 前端开发配置（VITE_API_BASE_URL等）

## 扩展性

项目设计考虑了扩展性，支持：
- 自定义数据集适配器
- 新增评估指标
- 集成新的模型提供商
- 扩展评估类型
- 前端组件的插件化开发

## 开发计划

### 第一阶段：基础架构（1-2周）
- Vue3项目初始化和基础配置
- Element Plus组件库集成
- 基础路由和布局实现

### 第二阶段：核心功能迁移（2-3周）
- 模型管理功能迁移
- 数据集管理功能迁移
- 简化的认证系统实现

### 第三阶段：高级功能（2-3周）
- 实时对话功能（WebSocket/SSE）
- 评估功能界面
- 性能测试界面

### 第四阶段：优化部署（1周）
- 性能优化和代码分割
- Docker容器化部署
- 生产环境配置

## 许可证

项目采用开源许可证，详情请参见 LICENSE 文件。
