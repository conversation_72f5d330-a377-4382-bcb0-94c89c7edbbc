@echo off
chcp 65001 >nul
echo ========================================
echo    LLM评估平台 - 开发环境启动脚本
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查Python是否安装
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 检查Node.js是否安装
echo [2/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH
    echo 请先安装Node.js 16+
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查前端依赖
echo [3/4] 检查前端依赖...
if not exist "llm-eval-frontend\node_modules" (
    echo 📦 首次运行，正在安装前端依赖...
    cd llm-eval-frontend
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
    echo ✅ 前端依赖安装完成
) else (
    echo ✅ 前端依赖已存在
)

:: 检查后端依赖
echo [4/4] 检查后端依赖...
pip show flask >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 正在安装后端依赖...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 后端依赖安装完成
) else (
    echo ✅ 后端依赖已存在
)

echo.
echo ========================================
echo           🚀 启动开发服务器
echo ========================================
echo.

:: 创建日志目录
if not exist "logs" mkdir logs

:: 启动后端服务器（在新窗口中）
echo 🔧 启动后端服务器 (Flask - 端口5000)...
start "LLM评估平台-后端" cmd /k "echo 后端服务器启动中... && python run_api.py"

:: 等待后端启动
echo ⏳ 等待后端服务器启动...
timeout /t 3 /nobreak >nul

:: 启动前端开发服务器（在新窗口中）
echo 🎨 启动前端开发服务器 (Vue+Vite - 端口3000)...
start "LLM评估平台-前端" cmd /k "cd llm-eval-frontend && echo 前端开发服务器启动中... && npm run dev"

:: 等待前端启动
echo ⏳ 等待前端服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo           ✅ 启动完成！
echo ========================================
echo.
echo 🌐 前端地址: http://localhost:3000
echo 🔧 后端API: http://localhost:5000
echo 🔍 健康检查: http://localhost:5000/health
echo.
echo 📝 默认账户:
echo    管理员: admin/admin
echo    测试用户: test/test
echo.
echo ⚠️  注意事项:
echo    - 前后端服务器将在新窗口中运行
echo    - 关闭对应窗口即可停止服务
echo    - 修改代码后会自动重载
echo.
echo 💡 提示: 按任意键打开前端页面...
pause >nul

:: 打开浏览器
start http://localhost:3000

echo.
echo 🎉 开发环境已就绪！祝您开发愉快！
echo.
pause
