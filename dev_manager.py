#!/usr/bin/env python3
"""
LLM评估平台开发环境管理器
一个简单的命令行面板，用于启动和停止前后端服务
"""

import os
import sys
import subprocess
import time
import signal
import psutil
import webbrowser
from pathlib import Path

class DevManager:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.project_root = Path(__file__).parent
        
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def show_menu(self):
        """显示主菜单"""
        self.clear_screen()
        print("=" * 50)
        print("    LLM评估平台 - 开发环境管理器")
        print("=" * 50)
        print()
        
        # 检查服务状态
        backend_status = "🟢 运行中" if self.is_backend_running() else "🔴 已停止"
        frontend_status = "🟢 运行中" if self.is_frontend_running() else "🔴 已停止"
        
        print(f"后端服务 (Flask):  {backend_status}")
        print(f"前端服务 (Vue):    {frontend_status}")
        print()
        print("请选择操作:")
        print("1. 启动后端服务")
        print("2. 启动前端服务") 
        print("3. 启动全部服务")
        print("4. 停止后端服务")
        print("5. 停止前端服务")
        print("6. 停止全部服务")
        print("7. 打开前端页面")
        print("8. 查看服务状态")
        print("0. 退出")
        print("=" * 50)
    
    def is_backend_running(self):
        """检查后端是否运行"""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'python.exe' or proc.info['name'] == 'python':
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'run_api.py' in cmdline:
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False
    
    def is_frontend_running(self):
        """检查前端是否运行"""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'node.exe' or proc.info['name'] == 'node':
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'vite' in cmdline.lower():
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False
    
    def start_backend(self):
        """启动后端服务"""
        if self.is_backend_running():
            print("后端服务已在运行中")
            return
            
        print("启动后端服务...")
        try:
            if os.name == 'nt':  # Windows
                self.backend_process = subprocess.Popen(
                    ['python', 'run_api.py'],
                    cwd=self.project_root,
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )
            else:  # Linux/Mac
                self.backend_process = subprocess.Popen(
                    ['python3', 'run_api.py'],
                    cwd=self.project_root
                )
            print("✅ 后端服务启动成功 (端口: 5000)")
            time.sleep(2)
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
    
    def start_frontend(self):
        """启动前端服务"""
        if self.is_frontend_running():
            print("前端服务已在运行中")
            return
            
        frontend_dir = self.project_root / 'llm-eval-frontend'
        if not frontend_dir.exists():
            print("❌ 前端目录不存在")
            return
            
        print("启动前端服务...")
        try:
            if os.name == 'nt':  # Windows
                self.frontend_process = subprocess.Popen(
                    ['npm', 'run', 'dev'],
                    cwd=frontend_dir,
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )
            else:  # Linux/Mac
                self.frontend_process = subprocess.Popen(
                    ['npm', 'run', 'dev'],
                    cwd=frontend_dir
                )
            print("✅ 前端服务启动成功 (端口: 3000)")
            time.sleep(3)
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
    
    def stop_backend(self):
        """停止后端服务"""
        print("停止后端服务...")
        killed = False
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'python.exe' or proc.info['name'] == 'python':
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'run_api.py' in cmdline:
                        proc.terminate()
                        killed = True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if killed:
            print("✅ 后端服务已停止")
        else:
            print("ℹ️ 未发现运行中的后端服务")
    
    def stop_frontend(self):
        """停止前端服务"""
        print("停止前端服务...")
        killed = False
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'node.exe' or proc.info['name'] == 'node':
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'vite' in cmdline.lower():
                        proc.terminate()
                        killed = True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if killed:
            print("✅ 前端服务已停止")
        else:
            print("ℹ️ 未发现运行中的前端服务")
    
    def open_frontend(self):
        """打开前端页面"""
        print("打开前端页面...")
        webbrowser.open('http://localhost:3000')
        print("✅ 已在浏览器中打开前端页面")
    
    def show_status(self):
        """显示详细状态"""
        print("\n服务状态详情:")
        print("-" * 30)
        
        backend_running = self.is_backend_running()
        frontend_running = self.is_frontend_running()
        
        print(f"后端服务: {'🟢 运行中' if backend_running else '🔴 已停止'}")
        if backend_running:
            print("  - 地址: http://localhost:5000")
            print("  - 健康检查: http://localhost:5000/health")
        
        print(f"前端服务: {'🟢 运行中' if frontend_running else '🔴 已停止'}")
        if frontend_running:
            print("  - 地址: http://localhost:3000")
        
        print("\n默认账户:")
        print("  - 管理员: admin/admin")
        print("  - 测试用户: test/test")
    
    def run(self):
        """运行主循环"""
        while True:
            self.show_menu()
            
            try:
                choice = input("请输入选项 (0-8): ").strip()
                
                if choice == '0':
                    print("退出程序...")
                    break
                elif choice == '1':
                    self.start_backend()
                elif choice == '2':
                    self.start_frontend()
                elif choice == '3':
                    self.start_backend()
                    self.start_frontend()
                elif choice == '4':
                    self.stop_backend()
                elif choice == '5':
                    self.stop_frontend()
                elif choice == '6':
                    self.stop_backend()
                    self.stop_frontend()
                elif choice == '7':
                    self.open_frontend()
                elif choice == '8':
                    self.show_status()
                else:
                    print("无效选项，请重新选择")
                
                if choice != '0':
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n程序被中断，正在退出...")
                break
            except Exception as e:
                print(f"发生错误: {e}")
                input("按回车键继续...")

if __name__ == '__main__':
    # 检查依赖
    try:
        import psutil
    except ImportError:
        print("缺少依赖包 psutil，正在安装...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'psutil'])
        import psutil
    
    manager = DevManager()
    manager.run()
