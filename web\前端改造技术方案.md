# LLM评估平台前端改造技术方案 (V2.0)

## 1. 项目概述

将现有的Flask模板渲染前端改造为基于**Vue 3**的现代化单页应用(SPA)，实现**彻底的前后端分离**架构。新前端将通过调用后端提供的**RESTful API**来获取数据和执行操作，旨在大幅提升用户体验、开发效率和项目可维护性。

## 2. 技术栈选型

### 核心框架
- **Vue 3.4+**: 使用Composition API，提供更好的TypeScript支持和性能。
- **TypeScript 5.0+**: 提供强类型安全和卓越的开发体验。
- **Vite 5.0+**: 现代化的构建工具，提供极速的开发服务器和构建速度。

### UI与状态管理
- **Element Plus**: 企业级UI组件库，用于快速构建专业界面。
- **Pinia**: Vue 3官方推荐的状态管理库，用于管理全局和模块化状态。
- **VueUse**: 便捷的组合式API工具库。

### 路由与网络
- **Vue Router 4**: Vue 3官方路由解决方案。
- **Axios**: 成熟的HTTP客户端，通过封装实现统一的API请求和响应处理。

### 开发与规范工具
- **ESLint + Prettier**: 保证代码风格和质量。
- **Husky + lint-staged**: 在Git提交前自动进行代码检查和格式化。
- **Commitizen + commitlint**: 强制使用规范化的Git提交信息。

## 3. 架构设计

### 3.1. 目录结构 (更新)
```
llm-eval-frontend/
├── src/
│   ├── api/                # API接口封装 (e.g., models.ts, auth.ts)
│   ├── assets/             # 静态资源 (styles, images)
│   ├── components/         # 可复用组件
│   │   ├── common/         # 全局通用组件 (e.g., SvgIcon, PageWrapper)
│   │   └── business/       # 业务相关组件 (e.g., ModelSelector, DatasetUploader)
│   ├── layouts/            # 页面布局
│   │   └── DefaultLayout.vue
│   ├── router/             # 路由配置
│   │   └── index.ts
│   ├── stores/             # Pinia状态管理
│   │   ├── auth.ts         # 认证Store
│   │   ├── models.ts       # 模型管理Store
│   │   └── index.ts        # (可选) 统一导出
│   ├── types/              # TypeScript类型定义 (api.ts, model.ts)
│   ├── utils/              # 工具函数 (request.ts, storage.ts)
│   ├── views/              # 页面级组件 (路由渲染的入口)
│   │   ├── auth/Login.vue
│   │   ├── dashboard/Index.vue
│   │   ├── models/Index.vue
│   │   └── ...
│   ├── App.vue             # 应用根组件
│   └── main.ts             # 应用入口文件
├── .husky/                 # Husky钩子
├── public/                 # 公共静态文件
├── package.json
└── vite.config.ts
```

### 3.2. 核心模块设计

#### a. 认证模块 (已完成)
- **目标**: 实现简化的、适合内部系统的登录认证。
- **实现**:
  - `Login.vue`: 提供登录界面。
  - `auth.ts` (Pinia Store): 管理`isAuthenticated`状态，并持久化到localStorage。
  - **路由守卫**: (下一步实现) 拦截未认证的路由请求，重定向到登录页。

#### b. 模型管理模块 (当前任务)
- **目标**: 迁移旧版的模型列表、添加、编辑和删除功能。
- **页面**: `views/models/Index.vue`
- **组件**:
  - `business/ModelTable.vue`: 展示模型列表，包含操作按钮。
  - `business/ModelForm.vue`: 用于创建和编辑模型的弹窗或独立页面。
- **API**:
  - `GET /api/models`: 获取模型列表 (支持分页、搜索)。
  - `POST /api/models`: 创建新模型。
  - `PUT /api/models/:id`: 更新模型。
  - `DELETE /api/models/:id`: 删除模型。
- **状态管理**:
  - `stores/models.ts`: 缓存模型列表，管理加载状态。

---

## 4. 改造实施计划 (更新)

### ✅ 第一阶段：基础架构搭建 (已完成)
- [x] 项目初始化 (Vue3+TS+Vite)
- [x] 安装核心依赖 (Element Plus, Pinia, Vue Router)
- [x] 配置开发工具 (ESLint, Prettier, Husky, Commitizen)
- [x] 封装Axios (`utils/request.ts`)
- [x] 搭建项目目录结构

### ➡️ 第二阶段：核心功能迁移 (进行中)
1.  **简化认证系统 (已完成)**
    - [x] 创建登录页面 (`Login.vue`)。
    - [x] 创建认证Store (`auth.ts`)。
    - [x] 实现动态导航栏和登入登出逻辑。
2.  **路由守卫**
    - [ ] 在`router/index.ts`中添加全局前置守卫，保护需要认证的页面。
3.  **模型管理**
    - [ ] 分析旧模板 `app/templates/models/`。
    - [ ] 创建 `views/models/Index.vue` 页面。
    - [ ] 创建 `ModelTable.vue` 和 `ModelForm.vue` 业务组件。
    - [ ] 封装模型管理的API (`api/models.ts`)。
    - [ ] 创建模型管理的Store (`stores/models.ts`)。

### 第三阶段：高级功能实现
- **对话模块**: 集成成熟聊天组件，实现多模型对话。
- **数据集管理**: 实现文件上传和数据预览。
- **评估模块**: 实现任务配置、进度监控和结果可视化。

### 第四阶段：优化和部署
- **性能优化**: 代码分割、懒加载、虚拟滚动。
- **测试**: 单元测试和端到端测试。
- **部署**: 使用Docker和Nginx进行容器化部署。

---

## 5. 总结

本方案V2.0版本更加聚焦于**API驱动**的开发模式和**组件化**的设计思想。通过细化模块设计和开发计划，我们可以更有条理地将旧系统功能迁移到现代化的Vue 3架构上，最终交付一个高性能、易维护的前后端分离应用。
