<template>
  <div>
    <n-space justify="space-between" align="center" style="margin-bottom: 24px;">
      <div>
        <h1 style="margin: 0; font-size: 24px; font-weight: 600;">效果评估</h1>
        <p style="margin: 8px 0 0 0; color: #909399;">
          评估模型在各种任务上的表现，生成详细的评估报告
        </p>
      </div>
      <n-button type="primary">
        <template #icon>
          <n-icon><Play /></n-icon>
        </template>
        开始评估
      </n-button>
    </n-space>

    <n-card>
      <n-empty description="效果评估功能正在开发中" size="large">
        <template #icon>
          <n-icon size="64" color="#d9d9d9">
            <BarChart />
          </n-icon>
        </template>
        <template #extra>
          <n-space>
            <n-button disabled>
              <template #icon>
                <n-icon><Time /></n-icon>
              </template>
              功能开发中
            </n-button>
            <n-button type="primary" @click="$router.push('/')">
              返回首页
            </n-button>
          </n-space>
        </template>
      </n-empty>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import {
  Play,
  BarChart,
  Time
} from '@vicons/ionicons5'
</script>
