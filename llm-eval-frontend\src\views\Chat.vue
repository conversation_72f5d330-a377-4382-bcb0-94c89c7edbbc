<template>
  <div>
    <n-space justify="space-between" align="center" style="margin-bottom: 24px;">
      <div>
        <h1 style="margin: 0; font-size: 24px; font-weight: 600;">智能对话</h1>
        <p style="margin: 8px 0 0 0; color: #909399;">
          与AI模型进行智能对话，体验先进的语言理解能力
        </p>
      </div>
      <n-button type="primary">
        <template #icon>
          <n-icon><Add /></n-icon>
        </template>
        新建对话
      </n-button>
    </n-space>

    <n-card>
      <n-empty description="对话功能正在开发中" size="large">
        <template #icon>
          <n-icon size="64" color="#d9d9d9">
            <ChatbubbleEllipses />
          </n-icon>
        </template>
        <template #extra>
          <n-space>
            <n-button disabled>
              <template #icon>
                <n-icon><Time /></n-icon>
              </template>
              功能开发中
            </n-button>
            <n-button type="primary" @click="$router.push('/')">
              返回首页
            </n-button>
          </n-space>
        </template>
      </n-empty>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import {
  Add,
  ChatbubbleEllipses,
  Time
} from '@vicons/ionicons5'
</script>
